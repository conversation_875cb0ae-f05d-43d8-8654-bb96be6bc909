import { config } from 'dotenv';

// Load environment variables
config({ path: '.env' });

/**
 * Scheduler configuration loaded from environment variables
 */
export const schedulerConfig = {
  // Task maintenance interval in hours
  maintenanceIntervalHours: parseInt(process.env.SCHEDULER_MAINTENANCE_INTERVAL_HOURS || '6'),
  
  // Initial delay in seconds before running first maintenance after startup
  initialDelaySeconds: parseInt(process.env.SCHEDULER_INITIAL_DELAY_SECONDS || '5'),
  
  // Email reminder configuration
  emailReminderHour: parseInt(process.env.EMAIL_REMINDER_HOUR || '6'),
  emailReminderMinute: parseInt(process.env.EMAIL_REMINDER_MINUTE || '0'),
  
  // Default number of days before due date to send reminders
  defaultReminderDays: parseInt(process.env.DEFAULT_REMINDER_DAYS || '3'),
  
  // Recurring tasks configuration
  recurringGenerationYears: parseInt(process.env.RECURRING_GENERATION_YEARS || '10'),
  recurringMaintenanceMonths: parseInt(process.env.RECURRING_MAINTENANCE_MONTHS || '3'),

  // Cron job configuration
  cronSecret: process.env.CRON_SECRET || 'dev-secret-change-in-production'
};

/**
 * Get scheduler configuration with validation
 */
export function getSchedulerConfig() {
  // Validate configuration values
  const config = { ...schedulerConfig };
  
  // Ensure positive values
  if (config.maintenanceIntervalHours <= 0) {
    console.warn('⚠️  Invalid SCHEDULER_MAINTENANCE_INTERVAL_HOURS, using default: 6');
    config.maintenanceIntervalHours = 6;
  }
  
  if (config.initialDelaySeconds < 0) {
    console.warn('⚠️  Invalid SCHEDULER_INITIAL_DELAY_SECONDS, using default: 5');
    config.initialDelaySeconds = 5;
  }
  
  if (config.emailReminderHour < 0 || config.emailReminderHour > 23) {
    console.warn('⚠️  Invalid EMAIL_REMINDER_HOUR (must be 0-23), using default: 6');
    config.emailReminderHour = 6;
  }
  
  if (config.emailReminderMinute < 0 || config.emailReminderMinute > 59) {
    console.warn('⚠️  Invalid EMAIL_REMINDER_MINUTE (must be 0-59), using default: 0');
    config.emailReminderMinute = 0;
  }
  
  if (config.defaultReminderDays <= 0) {
    console.warn('⚠️  Invalid DEFAULT_REMINDER_DAYS, using default: 3');
    config.defaultReminderDays = 3;
  }
  
  if (config.recurringGenerationYears <= 0) {
    console.warn('⚠️  Invalid RECURRING_GENERATION_YEARS, using default: 10');
    config.recurringGenerationYears = 10;
  }
  
  if (config.recurringMaintenanceMonths <= 0) {
    console.warn('⚠️  Invalid RECURRING_MAINTENANCE_MONTHS, using default: 3');
    config.recurringMaintenanceMonths = 3;
  }
  
  return config;
}

/**
 * Log current scheduler configuration
 */
export function logSchedulerConfig() {
  const config = getSchedulerConfig();
  
  console.log('📋 Scheduler Configuration:');
  console.log(`  🔄 Maintenance interval: ${config.maintenanceIntervalHours} hours`);
  console.log(`  ⏱️  Initial delay: ${config.initialDelaySeconds} seconds`);
  console.log(`  📧 Email reminder time: ${config.emailReminderHour.toString().padStart(2, '0')}:${config.emailReminderMinute.toString().padStart(2, '0')}`);
  console.log(`  📅 Default reminder days: ${config.defaultReminderDays} days`);
  console.log(`  🔁 Recurring generation: ${config.recurringGenerationYears} years`);
  console.log(`  🔧 Recurring maintenance: ${config.recurringMaintenanceMonths} months`);
}

export default schedulerConfig;
